import axios from 'axios'
import { authService } from './authService'

// Create axios instance with base configuration
const apiClient = axios.create({
  baseURL: process.env.VUE_APP_API_BASE_URL, // Replace with your .NET backend URL
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  async (config) => {
    try {
      if (authService.isAuthenticated()) {
        const token = await authService.getAccessToken()
        config.headers.Authorization = `Bearer ${token}`
      }
    } catch (error) {
      console.error('Failed to get access token:', error)
      // Optionally redirect to login or handle error
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle common errors
apiClient.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      console.error('Unauthorized request - token may be expired')
      // Optionally trigger re-authentication
    } else if (error.response?.status >= 500) {
      console.error('Server error:', error.response.data)
    }
    return Promise.reject(error)
  }
)

class ApiService {
  // Generic HTTP methods
  async get(endpoint, config = {}) {
    try {
      const response = await apiClient.get(endpoint, config)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async post(endpoint, data = {}, config = {}) {
    try {
      const response = await apiClient.post(endpoint, data, config)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async put(endpoint, data = {}, config = {}) {
    try {
      const response = await apiClient.put(endpoint, data, config)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async delete(endpoint, config = {}) {
    try {
      const response = await apiClient.delete(endpoint, config)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  // Example API endpoints - customize based on your backend
  async getUserProfile() {
    return this.get('/user/profile')
  }

  async updateUserProfile(profileData) {
    return this.put('/user/profile', profileData)
  }

  async getUsers() {
    return this.get('/users')
  }

  async createUser(userData) {
    return this.post('/users', userData)
  }

  async getUserById(id) {
    return this.get(`/users/${id}`)
  }

  async updateUser(id, userData) {
    return this.put(`/users/${id}`, userData)
  }

  async deleteUser(id) {
    return this.delete(`/users/${id}`)
  }

  // Health check endpoint
  async healthCheck() {
    return this.get('/health')
  }

  // Error handler
  handleError(error) {
    const errorMessage = error.response?.data?.message || error.message || 'An unexpected error occurred'
    const statusCode = error.response?.status || 500
    
    return {
      message: errorMessage,
      statusCode,
      originalError: error,
    }
  }
}

export const apiService = new ApiService()
export { apiClient }