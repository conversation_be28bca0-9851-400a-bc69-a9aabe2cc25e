import { PublicClientApplication, LogLevel } from '@azure/msal-browser'

// MSAL Configuration
const msalConfig = {
  auth: {
    clientId: process.env.VUE_APP_CLIENT_ID, // Replace with your Entra ID app registration client ID
    authority: process.env.VUE_APP_API_AUTHORITY, // Replace with your tenant ID
    redirectUri: window.location.origin,
  },
  cache: {
    cacheLocation: 'sessionStorage',
    storeAuthStateInCookie: false,
  },
  system: {
    loggerOptions: {
      loggerCallback: (level, message, containsPii) => {
        if (containsPii) return
        switch (level) {
          case LogLevel.Error:
            console.error(message)
            return
          case LogLevel.Info:
            console.info(message)
            return
          case LogLevel.Verbose:
            console.debug(message)
            return
          case LogLevel.Warning:
            console.warn(message)
            return
        }
      },
    },
  },
}

// Request configuration for login
const loginRequest = {
  scopes: ['User.Read', process.env.VUE_APP_API_SCOPE], // Add your API scope
}

// Request configuration for token acquisition
const tokenRequest = {
  scopes: [process.env.VUE_APP_API_SCOPE], // Your backend API scope
  account: null,
}

class AuthService {
  constructor() {
    this.msalInstance = null
    this.account = null
  }

  async initialize() {
    try {
      this.msalInstance = new PublicClientApplication(msalConfig)
      await this.msalInstance.initialize()
      
      // Handle redirect promise
      const response = await this.msalInstance.handleRedirectPromise()
      if (response) {
        this.account = response.account
      } else {
        // Check if user is already logged in
        const accounts = this.msalInstance.getAllAccounts()
        if (accounts.length > 0) {
          this.account = accounts[0]
        }
      }
    } catch (error) {
      console.error('MSAL initialization failed:', error)
      throw error
    }
  }

  async login() {
    try {
      const response = await this.msalInstance.loginPopup(loginRequest)
      this.account = response.account
      return response
    } catch (error) {
      console.error('Login failed:', error)
      throw error
    }
  }

  async logout() {
    try {
      await this.msalInstance.logoutPopup({
        account: this.account,
      })
      this.account = null
    } catch (error) {
      console.error('Logout failed:', error)
      throw error
    }
  }

  async getAccessToken() {
    if (!this.account) {
      throw new Error('No account available')
    }

    tokenRequest.account = this.account

    try {
      // Try to get token silently first
      const response = await this.msalInstance.acquireTokenSilent(tokenRequest)
      return response.accessToken
    } catch (error) {
      console.warn('Silent token acquisition failed, trying popup:', error)
      
      // If silent acquisition fails, try popup
      try {
        const response = await this.msalInstance.acquireTokenPopup(tokenRequest)
        return response.accessToken
      } catch (popupError) {
        console.error('Token acquisition failed:', popupError)
        throw popupError
      }
    }
  }

  isAuthenticated() {
    return this.account !== null
  }

  getAccount() {
    return this.account
  }

  getUserInfo() {
    if (!this.account) return null
    
    return {
      id: this.account.homeAccountId,
      username: this.account.username,
      name: this.account.name,
      email: this.account.username,
    }
  }
}

export const authService = new AuthService()