<template>
  <v-container class="pa-0 elevation-0" style="max-width: 100% !important">
    <v-list
      ref="scrollContainer"
      height="calc(100vh - 112px)"
      style="overflow-y: scroll"
      class="py-0"
      lines="two"
    >
      <v-skeleton-loader
        :loading="accommodationStore.fetchingAccommodations"
        type="
      list-item-avatar-two-line,
      list-item-avatar-two-line,
      list-item-avatar-two-line
      "
      >
        <v-card
          v-if="accommodationStore.accommodations.length === 0"
          title="No results found"
          subtitle="No listings match your search query"
          prepend-icon="search_off"
          class="w-100 pa-2 elevation-0 no-results-card"
        ></v-card>
        <template v-else>
          <v-list-item
            v-for="(accommodation, accommodationIndex) in accommodationStore.accommodations"
            :key="accommodationIndex"
            :title="accommodation.name"
            :to="`/Accommodation/${accommodation.id}`"
            target="_blank"
            class="motel-list-item w-100"
            :class="{
              'motel-item-border-top': accommodationIndex === 0,
              'motel-item-border-bottom': accommodationIndex !== accommodationStore.accommodations - 1,
              'motel-list-item-inactive': accommodation.inactive,
            }"
          >
            <template #prepend>
              <v-avatar color="primary">
                <v-icon>night_shelter</v-icon>
              </v-avatar>
            </template>
            <template #subtitle>
              <div style="opacity: 0.6">
                {{ accommodation.street }}, {{ accommodation.suburb }} VIC
                {{ accommodation.postcode }}
              </div>
            </template>
          </v-list-item>
        </template>
      </v-skeleton-loader>
    </v-list>
  </v-container>
</template>

<script setup>
import { onBeforeUnmount } from 'vue'
import { useAccommodationStore } from '../stores/accommodationStore'
import { useAuthStore } from '../stores/authStore'

const authStore = useAuthStore()
const accommodationStore = useAccommodationStore()

onBeforeUnmount(async () => {
  if (authStore.isAuthenticated) {
    await accommodationStore.fetchAccommodations();
  }
})
</script>

<style lang="scss" scoped>
.motel-item-border-bottom {
  border-bottom: 0.1px solid #a5a5a5;
}
.motel-item-border-top {
  border-top: 0.1px solid #a5a5a5;
}
.motel-index-tab {
  text-transform: none !important;
  font-size: 14px !important;
  border-radius: 0 !important;
}
.no-results-card {
  :deep(.v-icon) {
    font-size: 30px !important;
  }
  :deep(.v-card-title) {
    margin-bottom: -2px !important;
  }
}
</style>
<style land="scss">
.motel-list-item .v-list-item-subtitle {
  opacity: 1 !important;
}
.motel-list-item-inactive {
  background: #e4e4e4 !important;
}
.no-results-card {
  :deep(.v-card__title) {
    font-size: 22px !important;
    font-weight: 500;
  }
}
</style>
