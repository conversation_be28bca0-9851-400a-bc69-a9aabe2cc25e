import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authService } from '../services/authService'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref(null)
  const loading = ref(false)
  const error = ref(null)

  // Getters
  const isAuthenticated = computed(() => {
    return authService.isAuthenticated() && user.value !== null
  })

  const userInfo = computed(() => {
    return user.value
  })

  // Actions
  const initializeAuth = async () => {
    loading.value = true
    error.value = null
    
    try {
      if (authService.isAuthenticated()) {
        user.value = authService.getUserInfo()
      }
    } catch (err) {
      error.value = err.message
      console.error('Auth initialization failed:', err)
    } finally {
      loading.value = false
    }
  }

  const login = async () => {
    loading.value = true
    error.value = null
    
    try {
      await authService.login()
      user.value = authService.getUserInfo()
    } catch (err) {
      error.value = err.message || 'Login failed'
      console.error('<PERSON><PERSON> failed:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const logout = async () => {
    loading.value = true
    error.value = null
    
    try {
      await authService.logout()
      user.value = null
    } catch (err) {
      error.value = err.message || 'Logout failed'
      console.error('Logout failed:', err)
    } finally {
      loading.value = false
    }
  }

  const getAccessToken = async () => {
    try {
      return await authService.getAccessToken()
    } catch (err) {
      error.value = err.message || 'Failed to get access token'
      throw err
    }
  }

  const clearError = () => {
    error.value = null
  }

  // Initialize auth state when store is created
  initializeAuth()

  return {
    // State
    user,
    loading,
    error,
    
    // Getters
    isAuthenticated,
    userInfo,
    
    // Actions
    login,
    logout,
    getAccessToken,
    clearError,
    initializeAuth,
  }
})