<template>
  <div class="login-wrapper">
    <v-card class="login-card" elevation="2" rounded="lg">
      <!-- Logo and title -->
      <v-card-title class="text-center pb-2">
        <div class="d-flex align-center justify-center">
          <v-icon color="primary" size="32" class="mr-2">holiday_village</v-icon>
          <h2 class="text-primary">EAMS</h2>
        </div>
      </v-card-title>

      <v-card-subtitle class="text-center pb-4">
        Sign in with Microsoft EntraID
      </v-card-subtitle>

      <v-card-text>
        <!-- Error Message -->
        <v-alert
          v-if="authStore.error"
          type="error"
          variant="tonal"
          class="mb-4"
          closable
          @click:close="authStore.clearError"
        >
          {{ authStore.error }}
        </v-alert>

        <!-- Microsoft Login Button -->
        <v-btn
          :loading="authStore.loading"
          @click="loginWithMicrosoft"
          color="primary"
          variant="elevated"
          block
          size="large"
          prepend-icon="login"
        >
          Sign in with Microsoft
        </v-btn>
      </v-card-text>
    </v-card>
  </div>
</template>

<script setup>
import { useAuthStore } from '../stores/authStore'
import { useRouter } from 'vue-router'

const authStore = useAuthStore()
const router = useRouter()

const loginWithMicrosoft = async () => {
  try {
    await authStore.login()
    router.push('/')
  } catch (error) {
    console.error('Login failed:', error)
  }
}
</script>

<style scoped lang="scss">
.sign-in-wrapper {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  background-color: whitesmoke;
}

.sign-in-container {
  max-width: 500px;
  width: 100%;
  margin: auto;
  padding: 20px 30px;
  background: white;
}

.terms-checkbox .v-input__details {
  margin-top: -20px !important;
}

.terms-link {
  font-weight: 500;
  text-decoration: underline;
  color: #1a237e;
  cursor: pointer;
}
</style>
