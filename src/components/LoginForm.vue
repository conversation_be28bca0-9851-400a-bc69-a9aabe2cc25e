<template>
  <div class="sign-in-wrapper">
    <div class="sign-in-container elevation-4 rounded-xl py-8">
      <!-- Logo and title -->
      <div class="d-flex justify-center mb-2">
        <v-icon>holiday_village</v-icon>
        <h3 class="ml-2">EAMS</h3>
      </div>

      <p class="text-center pb-0 mb-2">Sign in with Microsoft</p>
      <v-divider class="my-4 mx-auto" />

      <!-- Error Message -->
      <v-scroll-y-transition>
        <v-alert v-if="authStore.error" type="error" variant="tonal" dense outlined class="mb-4">
          {{ authStore.error }}
        </v-alert>
      </v-scroll-y-transition>

      <!-- Microsoft Login Button -->
      <v-btn
        :disabled="authStore.loading"
        @click="loginWithMicrosoft"
        color="primary"
        class="mt-4 elevation-0"
        block
        height="42px"
      >
        <span v-if="!authStore.loading">Sign in with Microsoft</span>
        <span v-else>Signing in...</span>
      </v-btn>
    </div>
  </div>
</template>

<script setup>
import { useAuthStore } from '../stores/authStore'
import { useRouter } from 'vue-router'

const authStore = useAuthStore()
const router = useRouter()

const loginWithMicrosoft = async () => {
  try {
    await authStore.login()
    router.push('/')
  } catch (error) {
    console.error('Login failed:', error)
  }
}
</script>

<style scoped lang="scss">
.sign-in-wrapper {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  background-color: whitesmoke;
}

.sign-in-container {
  max-width: 500px;
  width: 100%;
  margin: auto;
  padding: 20px 30px;
  background: white;
}

.terms-checkbox .v-input__details {
  margin-top: -20px !important;
}

.terms-link {
  font-weight: 500;
  text-decoration: underline;
  color: #1a237e;
  cursor: pointer;
}
</style>
