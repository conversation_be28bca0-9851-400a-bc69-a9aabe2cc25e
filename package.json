{"name": "EAMS", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "start": "node server.js"}, "dependencies": {"@azure/msal-browser": "^4.21.0", "axios": "^1.6.2", "core-js": "^3.34.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "pinia": "^2.1.7", "vue": "^3.4.29", "vue-router": "^4.5.1"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "sass": "^1.90.0", "sass-loader": "^16.0.5"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}